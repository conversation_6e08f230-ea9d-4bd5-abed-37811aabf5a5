{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2022", "module": "amd", "lib": ["ES2022"], "outFile": "./dist/index.d.ts", "declaration": true, "emitDeclarationOnly": true, "declarationMap": false, "sourceMap": false, "removeComments": false, "skipLibCheck": true, "moduleResolution": "node", "baseUrl": "./src", "paths": {"@/*": ["*"], "@/types/*": ["types/*"], "@/utils/*": ["utils/*"], "@/parsers/*": ["parsers/*"], "@/processors/*": ["processors/*"], "@/core/*": ["core/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts", "src/examples/**/*"]}