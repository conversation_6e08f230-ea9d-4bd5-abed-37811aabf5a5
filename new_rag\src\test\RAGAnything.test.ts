/**
 * RAGAnything核心类测试
 */

import { RAGAnything } from '../core';
import { LLMModelFunction, VisionModelFunction, EmbeddingFunction } from '../types';

// 模拟函数
const mockLLMFunction: LLMModelFunction = jest.fn().mockResolvedValue('Mock LLM response');
const mockVisionFunction: VisionModelFunction = jest.fn().mockResolvedValue('Mock Vision response');
const mockEmbeddingFunction: EmbeddingFunction = jest.fn().mockResolvedValue([[0.1, 0.2, 0.3]]);

// 模拟MinerU检查
jest.mock('../parsers/MineruParser', () => ({
  MineruParser: {
    checkInstallation: jest.fn().mockResolvedValue(true),
    checkLibreOfficeInstallation: jest.fn().mockResolvedValue(true),
    parseDocument: jest.fn().mockResolvedValue({
      contentList: [
        { type: 'text', text: 'Test content' }
      ],
      mdContent: 'Test markdown content',
      metadata: {
        totalBlocks: 1,
        blockTypes: { text: 1 },
        textLength: 20
      }
    })
  }
}));

describe('RAGAnything', () => {
  let rag: RAGAnything;

  beforeEach(() => {
    rag = new RAGAnything({
      workingDir: './test_rag_storage',
      llmModelFunc: mockLLMFunction,
      visionModelFunc: mockVisionFunction,
      embeddingFunc: mockEmbeddingFunction
    });
  });

  afterEach(async () => {
    if (rag.isInitialized()) {
      await rag.cleanup();
    }
  });

  describe('初始化', () => {
    test('应该成功创建RAGAnything实例', () => {
      expect(rag).toBeInstanceOf(RAGAnything);
      expect(rag.isInitialized()).toBe(false);
    });

    test('应该成功初始化', async () => {
      await rag.initialize();
      expect(rag.isInitialized()).toBe(true);
    });

    test('重复初始化应该不会出错', async () => {
      await rag.initialize();
      await rag.initialize(); // 第二次调用
      expect(rag.isInitialized()).toBe(true);
    });

    test('没有LLM函数时应该抛出错误', async () => {
      const ragWithoutLLM = new RAGAnything({
        workingDir: './test_rag_storage'
      });

      await expect(ragWithoutLLM.initialize()).rejects.toThrow('llm_model_func is required');
    });
  });

  describe('配置和信息', () => {
    test('应该返回正确的工作目录', () => {
      expect(rag.getWorkingDir()).toBe('./test_rag_storage');
    });

    test('应该返回处理器信息', async () => {
      await rag.initialize();
      const info = await rag.getProcessorInfo();

      expect(info.status).toBe('Initialized');
      expect(info.models.llmModel).toBe('External function');
      expect(info.models.visionModel).toBe('External function');
      expect(info.models.embeddingModel).toBe('External function');
      expect(info.processors).toHaveProperty('image');
      expect(info.processors).toHaveProperty('table');
      expect(info.processors).toHaveProperty('equation');
      expect(info.processors).toHaveProperty('generic');
    });

    test('应该检查MinerU安装状态', async () => {
      const installed = await rag.checkMineruInstallation();
      expect(typeof installed).toBe('boolean');
    });
  });

  describe('支持的格式', () => {
    test('应该返回支持的文件格式列表', () => {
      const formats = RAGAnything.getSupportedFormats();
      expect(Array.isArray(formats)).toBe(true);
      expect(formats).toContain('.pdf');
      expect(formats).toContain('.jpg');
      expect(formats).toContain('.docx');
    });

    test('应该正确检查文件格式支持', () => {
      expect(RAGAnything.isSupportedFormat('test.pdf')).toBe(true);
      expect(RAGAnything.isSupportedFormat('test.jpg')).toBe(true);
      expect(RAGAnything.isSupportedFormat('test.xyz')).toBe(false);
    });
  });

  describe('查询功能', () => {
    test('应该处理多模态查询', async () => {
      await rag.initialize();
      
      const result = await rag.queryWithMultimodal('测试查询', { mode: 'hybrid' });
      
      expect(result).toHaveProperty('answer');
      expect(result).toHaveProperty('sources');
      expect(result).toHaveProperty('metadata');
      expect(typeof result.answer).toBe('string');
    });

    test('未初始化时查询应该自动初始化', async () => {
      expect(rag.isInitialized()).toBe(false);
      
      const result = await rag.queryWithMultimodal('测试查询');
      
      expect(rag.isInitialized()).toBe(true);
      expect(result).toHaveProperty('answer');
    });
  });

  describe('清理功能', () => {
    test('应该正确清理资源', async () => {
      await rag.initialize();
      expect(rag.isInitialized()).toBe(true);
      
      await rag.cleanup();
      expect(rag.isInitialized()).toBe(false);
    });
  });

  describe('错误处理', () => {
    test('处理不存在的文件应该抛出错误', async () => {
      await rag.initialize();
      
      await expect(
        rag.processDocumentComplete('./non-existent-file.pdf')
      ).rejects.toThrow();
    });

    test('处理不支持的文件格式应该抛出错误', async () => {
      await rag.initialize();
      
      // 创建一个临时的不支持格式文件
      const fs = require('fs-extra');
      const testFile = './test.xyz';
      await fs.writeFile(testFile, 'test content');
      
      try {
        await expect(
          rag.processDocumentComplete(testFile)
        ).rejects.toThrow();
      } finally {
        await fs.remove(testFile);
      }
    });
  });
});
