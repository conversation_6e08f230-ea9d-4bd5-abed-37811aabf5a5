{"name": "rag-anything-ts", "version": "1.0.0", "description": "RAG-Anything: All-in-One RAG System - TypeScript Implementation", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "node scripts/build.js", "build:types": "npx dts-bundle-generator --config ./dts-bundle-generator.config.js", "dev": "node scripts/dev.js", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "<PERSON><PERSON><PERSON> dist", "prepare": "npm run build", "example:basic": "ts-node src/examples/basic-example.ts", "example:openai": "ts-node src/examples/openai-example.ts", "example:processor": "ts-node src/examples/processor-example.ts", "example:parser": "ts-node src/examples/parser-example.ts"}, "keywords": ["rag", "multimodal", "ai", "document-processing", "knowledge-graph", "typescript", "llm", "embedding"], "author": "<PERSON><PERSON><PERSON> (TypeScript port)", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HKUDS/RAG-Anything"}, "dependencies": {"axios": "^1.6.0", "form-data": "^4.0.0", "fs-extra": "^11.1.1", "mammoth": "^1.6.0", "mime-types": "^2.1.35", "node-fetch": "^3.3.2", "pdf-parse": "^1.1.1", "sharp": "^0.33.0", "uuid": "^9.0.1", "winston": "^3.11.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.8", "@types/mime-types": "^2.1.4", "@types/node": "^20.9.0", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsc-alias": "^1.8.16", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0"}, "files": ["dist/**/*", "README.md", "LICENSE"]}