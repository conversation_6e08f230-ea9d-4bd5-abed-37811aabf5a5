/**
 * 处理器测试
 */

import {
  ImageModalProcessor,
  TableModalProcessor,
  EquationModalProcessor,
  GenericModalProcessor
} from '../processors';
import {
  ImageContent,
  TableContent,
  EquationContent,
  LLMModelFunction,
  VisionModelFunction
} from '../types';

// 模拟函数
const mockLLMFunction: LLMModelFunction = jest.fn().mockResolvedValue('Mock LLM analysis result');
const mockVisionFunction: VisionModelFunction = jest.fn().mockResolvedValue('Mock Vision analysis result');

describe('Modal Processors', () => {
  describe('ImageModalProcessor', () => {
    let processor: ImageModalProcessor;

    beforeEach(() => {
      processor = new ImageModalProcessor({
        modalCaptionFunc: mockVisionFunction,
        maxRetries: 3,
        timeout: 30000
      });
    });

    test('应该正确处理图像内容', async () => {
      const imageContent: ImageContent = {
        type: 'image',
        img_path: './test-image.jpg',
        img_caption: ['测试图像'],
        img_footnote: ['测试注释'],
        img_id: 'test_img_001'
      };

      // 模拟文件存在检查和图像处理
      const fs = require('fs-extra');
      jest.spyOn(fs, 'pathExists').mockResolvedValue(true);
      jest.spyOn(fs, 'readFile').mockResolvedValue(Buffer.from('fake image data'));
      
      // 模拟Sharp图像处理
      const mockSharp = {
        resize: jest.fn().mockReturnThis(),
        jpeg: jest.fn().mockReturnThis(),
        toBuffer: jest.fn().mockResolvedValue(Buffer.from('processed image data')),
        metadata: jest.fn().mockResolvedValue({ width: 800, height: 600, format: 'jpeg' })
      };
      jest.doMock('sharp', () => jest.fn(() => mockSharp));

      try {
        const [description, entityInfo] = await processor.processMultimodalContent(
          imageContent,
          'image',
          'test.pdf',
          'TestImage'
        );

        expect(typeof description).toBe('string');
        expect(description.length).toBeGreaterThan(0);
        expect(entityInfo).toHaveProperty('entity_name');
        expect(entityInfo).toHaveProperty('entity_type');
        expect(entityInfo).toHaveProperty('summary');
        expect(entityInfo.entity_name).toBe('TestImage');
      } catch (error) {
        // 如果Sharp或文件操作失败，这是预期的
        expect(error).toBeDefined();
      }
    });

    test('应该验证图像内容格式', async () => {
      const invalidContent = {
        type: 'image' as const,
        // 缺少必需的img_path
      };

      await expect(
        processor.processMultimodalContent(invalidContent as any, 'image')
      ).rejects.toThrow();
    });

    test('应该处理错误的内容类型', async () => {
      const imageContent: ImageContent = {
        type: 'image',
        img_path: './test-image.jpg'
      };

      await expect(
        processor.processMultimodalContent(imageContent, 'table' as any)
      ).rejects.toThrow();
    });
  });

  describe('TableModalProcessor', () => {
    let processor: TableModalProcessor;

    beforeEach(() => {
      processor = new TableModalProcessor({
        modalCaptionFunc: mockLLMFunction,
        maxRetries: 3,
        timeout: 30000
      });
    });

    test('应该正确处理表格内容', async () => {
      const tableContent: TableContent = {
        type: 'table',
        table_body: `
| 产品 | 销量 | 价格 |
|------|------|------|
| A    | 100  | 50   |
| B    | 200  | 30   |
        `,
        table_caption: ['销售数据表'],
        table_footnote: ['2024年数据']
      };

      const [description, entityInfo] = await processor.processMultimodalContent(
        tableContent,
        'table',
        'sales.pdf',
        'SalesTable'
      );

      expect(typeof description).toBe('string');
      expect(description.length).toBeGreaterThan(0);
      expect(entityInfo.entity_name).toBe('SalesTable');
      expect(entityInfo.entity_type).toBeDefined();
      expect(description).toContain('表格');
    });

    test('应该分析表格结构', async () => {
      const tableContent: TableContent = {
        type: 'table',
        table_body: `
| 名称 | 数值 | 百分比 |
|------|------|--------|
| 项目1 | 100  | 25%    |
| 项目2 | 200  | 50%    |
| 项目3 | 100  | 25%    |
        `
      };

      const [description] = await processor.processMultimodalContent(
        tableContent,
        'table'
      );

      expect(description).toContain('行');
      expect(description).toContain('列');
    });

    test('应该验证表格内容', async () => {
      const invalidContent = {
        type: 'table' as const,
        // 缺少table_body
      };

      await expect(
        processor.processMultimodalContent(invalidContent as any, 'table')
      ).rejects.toThrow();
    });
  });

  describe('EquationModalProcessor', () => {
    let processor: EquationModalProcessor;

    beforeEach(() => {
      processor = new EquationModalProcessor({
        modalCaptionFunc: mockLLMFunction,
        maxRetries: 3,
        timeout: 30000
      });
    });

    test('应该正确处理公式内容', async () => {
      const equationContent: EquationContent = {
        type: 'equation',
        latex: '\\int_{0}^{\\infty} e^{-x} dx = 1',
        equation_caption: ['指数函数积分'],
        equation_footnote: ['基础积分公式']
      };

      const [description, entityInfo] = await processor.processMultimodalContent(
        equationContent,
        'equation',
        'math.pdf',
        'ExponentialIntegral'
      );

      expect(typeof description).toBe('string');
      expect(description.length).toBeGreaterThan(0);
      expect(entityInfo.entity_name).toBe('ExponentialIntegral');
      expect(description).toContain('公式');
      expect(description).toContain('\\int');
    });

    test('应该分析公式复杂度', async () => {
      const complexEquation: EquationContent = {
        type: 'equation',
        latex: '\\sum_{n=1}^{\\infty} \\frac{1}{n^2} = \\frac{\\pi^2}{6}'
      };

      const [description] = await processor.processMultimodalContent(
        complexEquation,
        'equation'
      );

      expect(description).toContain('公式');
    });

    test('应该验证公式内容', async () => {
      const invalidContent = {
        type: 'equation' as const,
        // 缺少latex
      };

      await expect(
        processor.processMultimodalContent(invalidContent as any, 'equation')
      ).rejects.toThrow();
    });
  });

  describe('GenericModalProcessor', () => {
    let processor: GenericModalProcessor;

    beforeEach(() => {
      processor = new GenericModalProcessor({
        modalCaptionFunc: mockLLMFunction,
        maxRetries: 3,
        timeout: 30000
      });
    });

    test('应该处理通用内容', async () => {
      const genericContent = {
        type: 'generic' as const,
        text: '自定义内容：这是一个自定义的内容类型',
        title: '自定义内容',
        description: '这是一个自定义的内容类型',
        data: { value: 123, text: 'test' }
      };

      const [description, entityInfo] = await processor.processMultimodalContent(
        genericContent,
        'generic',
        'custom.json',
        'CustomContent'
      );

      expect(typeof description).toBe('string');
      expect(description.length).toBeGreaterThan(0);
      expect(entityInfo.entity_name).toBe('CustomContent');
      expect(entityInfo.entity_type).toBeDefined();
    });

    test('应该处理自定义内容类型', async () => {
      const customData = {
        type: 'audio',
        file_path: './audio.mp3',
        duration: 120,
        transcript: '音频转录文本'
      };

      const [description, entityInfo] = await processor.processCustomContent(
        customData,
        'audio',
        'audio.mp3',
        'AudioContent'
      );

      expect(typeof description).toBe('string');
      expect(entityInfo.entity_name).toBe('AudioContent');
    });

    test('应该检查支持的内容类型', () => {
      const supportedTypes = GenericModalProcessor.getSupportedTypes();
      expect(Array.isArray(supportedTypes)).toBe(true);
      expect(supportedTypes).toContain('generic');
      expect(supportedTypes).toContain('custom');

      expect(GenericModalProcessor.canProcess('generic')).toBe(true);
      expect(GenericModalProcessor.canProcess('custom')).toBe(true);
      expect(GenericModalProcessor.canProcess('unknown_type')).toBe(false);
    });
  });

  describe('处理器错误处理', () => {
    test('应该处理模型调用失败', async () => {
      const failingLLMFunction: LLMModelFunction = jest.fn().mockRejectedValue(new Error('API调用失败'));
      
      const processor = new TableModalProcessor({
        modalCaptionFunc: failingLLMFunction,
        maxRetries: 1,
        timeout: 1000
      });

      const tableContent: TableContent = {
        type: 'table',
        table_body: '| A | B |\n|---|---|\n| 1 | 2 |'
      };

      // 应该返回备用描述而不是抛出错误
      const [description, entityInfo] = await processor.processMultimodalContent(
        tableContent,
        'table'
      );

      expect(typeof description).toBe('string');
      expect(description.length).toBeGreaterThan(0);
      expect(entityInfo).toBeDefined();
    });

    test('应该处理无效的内容对象', async () => {
      const processor = new GenericModalProcessor({
        modalCaptionFunc: mockLLMFunction
      });

      await expect(
        processor.processMultimodalContent(null as any, 'generic')
      ).rejects.toThrow();

      await expect(
        processor.processMultimodalContent(undefined as any, 'generic')
      ).rejects.toThrow();
    });
  });
});
