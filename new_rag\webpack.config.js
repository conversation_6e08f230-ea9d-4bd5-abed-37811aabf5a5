/**
 * Webpack配置文件
 * 用于生成CommonJS格式的打包文件
 */

const path = require('path');

module.exports = {
  entry: './src/index.ts',
  target: 'node',
  mode: 'production',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'index.js',
    library: {
      type: 'commonjs2'
    },
    clean: false // 不清理dist目录，保留其他文件
  },
  externals: {
    // 外部依赖，不打包进bundle
    'winston': 'winston',
    'fs-extra': 'fs-extra',
    'path': 'path',
    'crypto': 'crypto',
    'child_process': 'child_process',
    'os': 'os',
    'util': 'util',
    'fs': 'fs',
    'sharp': 'sharp' // 将sharp作为外部依赖
  },
  resolve: {
    extensions: ['.ts', '.js'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@/types': path.resolve(__dirname, 'src/types'),
      '@/utils': path.resolve(__dirname, 'src/utils'),
      '@/parsers': path.resolve(__dirname, 'src/parsers'),
      '@/processors': path.resolve(__dirname, 'src/processors'),
      '@/core': path.resolve(__dirname, 'src/core')
    }
  },
  module: {
    rules: [
      {
        test: /\.ts$/,
        use: {
          loader: 'ts-loader',
          options: {
            configFile: 'tsconfig.build.json'
          }
        },
        exclude: [
          /node_modules/,
          /\.test\.ts$/,
          /\.spec\.ts$/,
          /src\/examples/,
          /src\/test/
        ]
      }
    ]
  },
  optimization: {
    minimize: false // 不压缩，保持可读性
  },
  devtool: 'source-map'
};
