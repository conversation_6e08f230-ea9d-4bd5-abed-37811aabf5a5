{"name": "dts-bundle-generator", "version": "9.5.1", "description": "DTS Bundle Generator", "main": "dist/bundle-generator.js", "typings": "dist/bundle-generator.d.ts", "bin": "dist/bin/dts-bundle-generator.js", "files": ["config-schema.d.ts", "dist/**/*.d.ts", "dist/**/*.js"], "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bugs": {"url": "https://github.com/timocov/dts-bundle-generator/issues"}, "homepage": "https://github.com/timocov/dts-bundle-generator", "dependencies": {"typescript": ">=5.0.2", "yargs": "^17.6.0"}, "license": "MIT", "readme": "README.md", "repository": {"type": "git", "url": "git+https://github.com/timocov/dts-bundle-generator.git"}, "engines": {"node": ">=14.0.0"}}