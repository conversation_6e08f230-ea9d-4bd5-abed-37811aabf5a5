/**
 * 集成测试
 */

import fs from 'fs-extra';
import path from 'path';
import { RAGAnything } from '../core';
import { LLMModelFunction, VisionModelFunction, EmbeddingFunction } from '../types';
import { ensureDir } from '../utils/fileUtils';

// 模拟函数
const mockLLMFunction: LLMModelFunction = jest.fn().mockImplementation(
  async (prompt: string) => {
    if (prompt.includes('JSON')) {
      return JSON.stringify({
        entity_name: 'Test Entity',
        entity_type: 'test',
        summary: 'This is a test entity summary'
      });
    }
    return `分析结果：${prompt.substring(0, 50)}...的详细分析内容`;
  }
);

const mockVisionFunction: VisionModelFunction = jest.fn().mockResolvedValue(
  '这是一个图像分析结果，包含了图像的详细描述和关键信息'
);

const mockEmbeddingFunction: EmbeddingFunction = jest.fn().mockImplementation(
  async (texts: string[]) => {
    return texts.map(() => Array.from({ length: 1536 }, () => Math.random()));
  }
);

// 模拟MinerU
jest.mock('../parsers/MineruParser', () => ({
  MineruParser: {
    checkInstallation: jest.fn().mockResolvedValue(true),
    checkLibreOfficeInstallation: jest.fn().mockResolvedValue(true),
    parseDocument: jest.fn().mockImplementation(async (filePath: string) => {
      const ext = path.extname(filePath).toLowerCase();
      
      if (ext === '.txt' || ext === '.md') {
        const content = await fs.readFile(filePath, 'utf-8');
        return {
          contentList: [
            { type: 'text', text: content }
          ],
          mdContent: content,
          metadata: {
            totalBlocks: 1,
            blockTypes: { text: 1 },
            textLength: content.length
          }
        };
      }
      
      // 模拟多模态内容
      return {
        contentList: [
          { type: 'text', text: '这是文档的文本内容' },
          {
            type: 'image',
            img_path: './test-image.jpg',
            img_caption: ['测试图像'],
            img_footnote: ['图像注释']
          },
          {
            type: 'table',
            table_body: '| 列1 | 列2 |\n|-----|-----|\n| 值1 | 值2 |',
            table_caption: ['测试表格']
          },
          {
            type: 'equation',
            latex: 'E = mc^2',
            equation_caption: ['爱因斯坦质能方程']
          }
        ],
        mdContent: '# 测试文档\n\n这是文档内容...',
        metadata: {
          totalBlocks: 4,
          blockTypes: { text: 1, image: 1, table: 1, equation: 1 },
          textLength: 100
        }
      };
    })
  }
}));

describe('集成测试', () => {
  let rag: RAGAnything;
  const testDir = './integration_test';
  const testWorkingDir = path.join(testDir, 'rag_storage');

  beforeAll(async () => {
    await ensureDir(testDir);
  });

  afterAll(async () => {
    if (rag && rag.isInitialized()) {
      await rag.cleanup();
    }
    await fs.remove(testDir);
  });

  beforeEach(() => {
    rag = new RAGAnything({
      workingDir: testWorkingDir,
      llmModelFunc: mockLLMFunction,
      visionModelFunc: mockVisionFunction,
      embeddingFunc: mockEmbeddingFunction
    });
  });

  afterEach(async () => {
    if (rag && rag.isInitialized()) {
      await rag.cleanup();
    }
  });

  describe('完整工作流测试', () => {
    test('应该完成端到端的文档处理流程', async () => {
      // 创建测试文档
      const testDocPath = path.join(testDir, 'test-document.txt');
      const testContent = `
# 测试文档

这是一个测试文档，用于验证RAG-Anything的完整功能。

## 章节1：介绍

这里是介绍内容。

## 章节2：数据

这里包含一些数据和信息。
      `;
      
      await fs.writeFile(testDocPath, testContent, 'utf-8');

      // 初始化RAG系统
      await rag.initialize();
      expect(rag.isInitialized()).toBe(true);

      // 处理文档
      await rag.processDocumentComplete(testDocPath, {
        outputDir: path.join(testDir, 'output'),
        parseMethod: 'auto',
        displayStats: true
      });

      // 验证模拟函数被调用
      expect(mockLLMFunction).toHaveBeenCalled();

      // 执行查询
      const queryResult = await rag.queryWithMultimodal(
        '请总结这个文档的主要内容',
        { mode: 'hybrid' }
      );

      expect(queryResult).toHaveProperty('answer');
      expect(queryResult).toHaveProperty('sources');
      expect(queryResult).toHaveProperty('metadata');
      expect(typeof queryResult.answer).toBe('string');
    });

    test('应该处理包含多模态内容的文档', async () => {
      // 创建模拟的多模态文档
      const testDocPath = path.join(testDir, 'multimodal-document.pdf');
      await fs.writeFile(testDocPath, 'fake pdf content'); // 创建假文件

      await rag.initialize();

      // 处理多模态文档
      await rag.processDocumentComplete(testDocPath, {
        outputDir: path.join(testDir, 'multimodal_output'),
        parseMethod: 'auto'
      });

      // 验证不同类型的处理器都被调用
      expect(mockLLMFunction).toHaveBeenCalled();
      expect(mockVisionFunction).toHaveBeenCalled();

      // 检查调用次数（文本、表格、公式处理）
      const llmCalls = (mockLLMFunction as jest.Mock).mock.calls;
      expect(llmCalls.length).toBeGreaterThan(0);
    });

    test('应该处理批量文档', async () => {
      // 创建多个测试文档
      const batchDir = path.join(testDir, 'batch_docs');
      await ensureDir(batchDir);

      const testDocs = [
        { name: 'doc1.txt', content: '第一个测试文档的内容' },
        { name: 'doc2.txt', content: '第二个测试文档的内容' },
        { name: 'doc3.md', content: '# 第三个文档\n\n这是Markdown格式' }
      ];

      for (const doc of testDocs) {
        await fs.writeFile(path.join(batchDir, doc.name), doc.content, 'utf-8');
      }

      await rag.initialize();

      // 批量处理
      const batchResult = await rag.processFolderComplete(batchDir, {
        outputDir: path.join(testDir, 'batch_output'),
        fileExtensions: ['.txt', '.md'],
        recursive: false,
        maxWorkers: 1
      });

      expect(batchResult.total).toBe(3);
      expect(batchResult.success).toBeGreaterThan(0);
      expect(batchResult.failed).toBeLessThanOrEqual(batchResult.total);
    });
  });

  describe('错误处理和恢复', () => {
    test('应该处理处理器失败的情况', async () => {
      // 创建会导致处理器失败的模拟函数
      const failingLLMFunction: LLMModelFunction = jest.fn()
        .mockRejectedValueOnce(new Error('第一次调用失败'))
        .mockResolvedValue('恢复后的响应');

      const ragWithFailure = new RAGAnything({
        workingDir: path.join(testDir, 'failure_test'),
        llmModelFunc: failingLLMFunction,
        visionModelFunc: mockVisionFunction,
        embeddingFunc: mockEmbeddingFunction
      });

      await ragWithFailure.initialize();

      const testDocPath = path.join(testDir, 'failure-test.txt');
      await fs.writeFile(testDocPath, '测试失败恢复', 'utf-8');

      // 处理应该继续，即使某些步骤失败
      await expect(
        ragWithFailure.processDocumentComplete(testDocPath)
      ).resolves.not.toThrow();

      await ragWithFailure.cleanup();
    });

    test('应该处理不支持的文件格式', async () => {
      await rag.initialize();

      const unsupportedFile = path.join(testDir, 'unsupported.xyz');
      await fs.writeFile(unsupportedFile, 'unsupported content');

      await expect(
        rag.processDocumentComplete(unsupportedFile)
      ).rejects.toThrow();
    });

    test('应该处理不存在的文件', async () => {
      await rag.initialize();

      await expect(
        rag.processDocumentComplete('./non-existent-file.pdf')
      ).rejects.toThrow();
    });
  });

  describe('性能和资源管理', () => {
    test('应该正确管理资源', async () => {
      await rag.initialize();
      expect(rag.isInitialized()).toBe(true);

      const processorInfo = await rag.getProcessorInfo();
      expect(processorInfo.status).toBe('Initialized');
      expect(Object.keys(processorInfo.processors)).toHaveLength(4);

      await rag.cleanup();
      expect(rag.isInitialized()).toBe(false);
    });

    test('应该处理并发查询', async () => {
      await rag.initialize();

      const queries = [
        '查询1：文档的主要内容是什么？',
        '查询2：有哪些重要的数据？',
        '查询3：结论是什么？'
      ];

      const queryPromises = queries.map(query =>
        rag.queryWithMultimodal(query, { mode: 'hybrid' })
      );

      const results = await Promise.all(queryPromises);

      expect(results).toHaveLength(3);
      results.forEach(result => {
        expect(result).toHaveProperty('answer');
        expect(typeof result.answer).toBe('string');
      });
    });
  });

  describe('配置和自定义', () => {
    test('应该支持自定义配置', async () => {
      const customRag = new RAGAnything({
        workingDir: path.join(testDir, 'custom_rag'),
        llmModelFunc: mockLLMFunction,
        visionModelFunc: mockVisionFunction,
        embeddingFunc: mockEmbeddingFunction
      });

      await customRag.initialize();

      expect(customRag.getWorkingDir()).toBe(path.join(testDir, 'custom_rag'));
      expect(customRag.isInitialized()).toBe(true);

      const info = await customRag.getProcessorInfo();
      expect(info.models.llmModel).toBe('External function');
      expect(info.models.visionModel).toBe('External function');
      expect(info.models.embeddingModel).toBe('External function');

      await customRag.cleanup();
    });

    test('应该支持不同的查询模式', async () => {
      await rag.initialize();

      const query = '测试查询';
      const modes = ['local', 'global', 'hybrid'] as const;

      for (const mode of modes) {
        const result = await rag.queryWithMultimodal(query, { mode });
        expect(result).toHaveProperty('answer');
        expect(result.metadata?.mode).toBe(mode);
      }
    });
  });
});
