/**
 * 核心类型定义
 * 定义RAG-Anything系统中使用的所有TypeScript接口和类型
 */

// ============================================================================
// 基础类型
// ============================================================================

export type ContentType = 'text' | 'image' | 'table' | 'equation' | 'generic';

export type ParseMethod = 'auto' | 'ocr' | 'txt';

export type QueryMode = 'local' | 'global' | 'hybrid';

export type ProcessorType = 'image' | 'table' | 'equation' | 'generic';

// ============================================================================
// 文档内容相关类型
// ============================================================================

/**
 * 文档内容块接口
 */
export interface ContentBlock {
  type: ContentType;
  text?: string;
  [key: string]: any;
}

/**
 * 图像内容接口
 */
export interface ImageContent extends ContentBlock {
  type: 'image';
  img_path: string;
  img_caption?: string[];
  img_footnote?: string[];
  img_id?: string;
  bbox?: number[];
}

/**
 * 表格内容接口
 */
export interface TableContent extends ContentBlock {
  type: 'table';
  table_body: string;
  table_caption?: string[];
  table_footnote?: string[];
  table_id?: string;
  bbox?: number[];
}

/**
 * 公式内容接口
 */
export interface EquationContent extends ContentBlock {
  type: 'equation';
  latex: string;
  equation_caption?: string[];
  equation_footnote?: string[];
  equation_id?: string;
  bbox?: number[];
}

/**
 * 文本内容接口
 */
export interface TextContent extends ContentBlock {
  type: 'text';
  text: string;
  text_id?: string;
  bbox?: number[];
}

/**
 * 通用内容接口
 */
export interface GenericContent extends ContentBlock {
  type: 'generic';
  text: string;
  title?: string;
  description?: string;
  data?: any;
  bbox?: number[];
}

/**
 * 通用多模态内容类型
 */
export type MultimodalContent = ImageContent | TableContent | EquationContent | TextContent | GenericContent;

// ============================================================================
// 解析相关类型
// ============================================================================

/**
 * 解析结果接口
 */
export interface ParseResult {
  contentList: ContentBlock[];
  mdContent: string;
  metadata?: {
    totalBlocks: number;
    blockTypes: Record<string, number>;
    textLength: number;
  };
}

/**
 * 解析选项接口
 */
export interface ParseOptions {
  method?: ParseMethod;
  lang?: string;
  backend?: string;
  startPage?: number;
  endPage?: number;
  formula?: boolean;
  table?: boolean;
  device?: string;
  source?: string;
}

// ============================================================================
// 实体和知识图谱相关类型
// ============================================================================

/**
 * 实体信息接口
 */
export interface EntityInfo {
  entity_name: string;
  entity_type: string;
  summary: string;
  source_id?: string;
  file_path?: string;
  created_at?: number;
}

/**
 * 文本块数据接口
 */
export interface ChunkData {
  tokens: number;
  content: string;
  chunk_order_index: number;
  full_doc_id: string;
  file_path: string;
}

/**
 * 节点数据接口
 */
export interface NodeData {
  entity_id: string;
  entity_type: string;
  description: string;
  source_id: string;
  file_path: string;
  created_at: number;
}

// ============================================================================
// 配置相关类型
// ============================================================================

/**
 * LLM模型函数类型
 */
export type LLMModelFunction = (
  prompt: string,
  systemPrompt?: string,
  historyMessages?: any[],
  ...args: any[]
) => Promise<string>;

/**
 * 视觉模型函数类型
 */
export type VisionModelFunction = (
  prompt: string,
  systemPrompt?: string,
  historyMessages?: any[],
  imageData?: string,
  ...args: any[]
) => Promise<string>;

/**
 * 嵌入函数类型
 */
export type EmbeddingFunction = (texts: string[]) => Promise<number[][]>;

/**
 * RAGAnything配置接口
 */
export interface RAGAnythingConfig {
  workingDir?: string;
  llmModelFunc?: LLMModelFunction;
  visionModelFunc?: VisionModelFunction;
  embeddingFunc?: EmbeddingFunction;
  ragBackend?: RAGBackend;
  autoInitializeBackend?: boolean;
}

/**
 * 处理选项接口
 */
export interface ProcessOptions {
  outputDir?: string;
  parseMethod?: ParseMethod;
  displayStats?: boolean;
  splitByCharacter?: string;
  splitByCharacterOnly?: boolean;
  docId?: string;
}

/**
 * 批处理选项接口
 */
export interface BatchProcessOptions extends ProcessOptions {
  fileExtensions?: string[];
  recursive?: boolean;
  maxWorkers?: number;
}

// ============================================================================
// 查询相关类型
// ============================================================================

/**
 * 查询参数接口
 */
export interface QueryParams {
  mode?: QueryMode;
  topK?: number;
  includeMetadata?: boolean;
}

/**
 * 查询结果接口
 */
export interface QueryResult {
  answer: string;
  sources?: string[];
  metadata?: any;
}

// ============================================================================
// 错误类型
// ============================================================================

/**
 * RAG错误基类
 */
export class RAGError extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'RAGError';
  }
}

/**
 * 解析错误
 */
export class ParseError extends RAGError {
  constructor(message: string, public filePath?: string) {
    super(message, 'PARSE_ERROR');
    this.name = 'ParseError';
  }
}

/**
 * 处理错误
 */
export class ProcessError extends RAGError {
  constructor(message: string, public contentType?: string) {
    super(message, 'PROCESS_ERROR');
    this.name = 'ProcessError';
  }
}

// ============================================================================
// RAG 后端集成接口
// ============================================================================

/**
 * RAG 后端接口
 */
export interface RAGBackend {
  // 初始化
  initialize(): Promise<void>;

  // 文本内容操作
  insertTextContent(
    content: string,
    filePath: string,
    options?: {
      splitByCharacter?: string;
      splitByCharacterOnly?: boolean;
      docId?: string;
    }
  ): Promise<void>;

  // 实体操作
  createEntity(entityInfo: EntityInfo, chunkId: string): Promise<void>;
  updateEntity(entityId: string, updates: Partial<EntityInfo>): Promise<void>;
  getEntity(entityId: string): Promise<EntityInfo | null>;

  // 关系操作
  createRelationship(
    sourceEntityId: string,
    targetEntityId: string,
    relationshipType: string,
    description: string,
    metadata?: any
  ): Promise<void>;

  // 文本块操作
  createTextChunk(chunkData: ChunkData): Promise<string>;
  getTextChunk(chunkId: string): Promise<ChunkData | null>;

  // 查询操作
  query(query: string, params?: QueryParams): Promise<QueryResult>;

  // 清理操作
  cleanup(): Promise<void>;
}

/**
 * 关系信息接口
 */
export interface RelationshipInfo {
  id: string;
  sourceEntityId: string;
  targetEntityId: string;
  relationshipType: string;
  description: string;
  weight?: number;
  metadata?: any;
  createdAt: number;
}

/**
 * 向量数据库接口
 */
export interface VectorDatabase {
  upsert(data: Record<string, any>): Promise<void>;
  search(query: string, topK?: number): Promise<any[]>;
  delete(ids: string[]): Promise<void>;
}

/**
 * 知识图谱接口
 */
export interface KnowledgeGraph {
  addNode(nodeId: string, nodeData: NodeData): Promise<void>;
  addEdge(sourceId: string, targetId: string, edgeData: any): Promise<void>;
  getNode(nodeId: string): Promise<NodeData | null>;
  getNeighbors(nodeId: string): Promise<string[]>;
  removeNode(nodeId: string): Promise<void>;
  removeEdge(sourceId: string, targetId: string): Promise<void>;
}

// ============================================================================
// 工具类型
// ============================================================================

/**
 * 可选属性工具类型
 */
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * 深度只读类型
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

/**
 * 异步函数返回类型提取器
 */
export type AsyncReturnType<T extends (...args: any) => Promise<any>> = T extends (
  ...args: any
) => Promise<infer R>
  ? R
  : any;
