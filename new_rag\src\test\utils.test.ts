/**
 * 工具函数测试
 */

import fs from 'fs-extra';
import path from 'path';
import {
  fileExists,
  ensureDir,
  getFileExtension,
  getFileNameWithoutExt,
  calculateStringHash,
  getFileCategory,
  isSupportedFileFormat,
  createTempFilePath,
  getFilesInDirectory
} from '../utils/fileUtils';
import {
  executeCommand,
  executeShellCommand,
  isCommandAvailable,
  buildMineruCommand
} from '../utils/commandUtils';
import {
  replacePromptVariables,
  buildPrompt
} from '../utils/prompts';
import { createLogger, getModuleLogger } from '../utils/logger';

describe('文件工具函数', () => {
  const testDir = './test_utils';
  const testFile = path.join(testDir, 'test.txt');

  beforeAll(async () => {
    await ensureDir(testDir);
    await fs.writeFile(testFile, 'test content', 'utf-8');
  });

  afterAll(async () => {
    await fs.remove(testDir);
  });

  test('fileExists - 应该正确检查文件存在性', async () => {
    expect(await fileExists(testFile)).toBe(true);
    expect(await fileExists('./non-existent-file.txt')).toBe(false);
  });

  test('ensureDir - 应该创建目录', async () => {
    const newDir = path.join(testDir, 'new_dir');
    await ensureDir(newDir);
    expect(await fs.pathExists(newDir)).toBe(true);
  });

  test('getFileExtension - 应该返回正确的文件扩展名', () => {
    expect(getFileExtension('test.pdf')).toBe('.pdf');
    expect(getFileExtension('document.docx')).toBe('.docx');
    expect(getFileExtension('image.JPG')).toBe('.jpg');
    expect(getFileExtension('noextension')).toBe('');
  });

  test('getFileNameWithoutExt - 应该返回不含扩展名的文件名', () => {
    expect(getFileNameWithoutExt('/path/to/file.pdf')).toBe('file');
    expect(getFileNameWithoutExt('document.docx')).toBe('document');
    expect(getFileNameWithoutExt('noextension')).toBe('noextension');
  });

  test('calculateStringHash - 应该生成一致的哈希值', () => {
    const content = 'test content';
    const hash1 = calculateStringHash(content);
    const hash2 = calculateStringHash(content);
    expect(hash1).toBe(hash2);
    expect(hash1).toHaveLength(32); // MD5哈希长度

    const hashWithPrefix = calculateStringHash(content, 'prefix-');
    expect(hashWithPrefix.startsWith('prefix-')).toBe(true);
  });

  test('getFileCategory - 应该正确分类文件', () => {
    expect(getFileCategory('document.pdf')).toBe('pdf');
    expect(getFileCategory('image.jpg')).toBe('image');
    expect(getFileCategory('image.PNG')).toBe('image');
    expect(getFileCategory('document.docx')).toBe('office');
    expect(getFileCategory('text.txt')).toBe('text');
    expect(getFileCategory('unknown.xyz')).toBe('unknown');
  });

  test('isSupportedFileFormat - 应该正确检查支持的格式', () => {
    expect(isSupportedFileFormat('document.pdf')).toBe(true);
    expect(isSupportedFileFormat('image.jpg')).toBe(true);
    expect(isSupportedFileFormat('document.docx')).toBe(true);
    expect(isSupportedFileFormat('text.txt')).toBe(true);
    expect(isSupportedFileFormat('unknown.xyz')).toBe(false);
  });

  test('createTempFilePath - 应该生成临时文件路径', () => {
    const tempPath1 = createTempFilePath('.txt');
    const tempPath2 = createTempFilePath('.txt');
    
    expect(tempPath1).toContain('rag_temp_');
    expect(tempPath1.endsWith('.txt')).toBe(true);
    expect(tempPath1).not.toBe(tempPath2); // 应该生成不同的路径
  });

  test('getFilesInDirectory - 应该获取目录中的文件', async () => {
    // 创建测试文件
    await fs.writeFile(path.join(testDir, 'test1.txt'), 'content1');
    await fs.writeFile(path.join(testDir, 'test2.pdf'), 'content2');
    await fs.writeFile(path.join(testDir, 'test3.xyz'), 'content3');

    const allFiles = await getFilesInDirectory(testDir);
    expect(allFiles.length).toBeGreaterThanOrEqual(3);

    const txtFiles = await getFilesInDirectory(testDir, ['.txt']);
    expect(txtFiles.length).toBeGreaterThanOrEqual(2);
    expect(txtFiles.every(file => file.endsWith('.txt'))).toBe(true);
  });
});

describe('命令执行工具', () => {
  test('executeShellCommand - 应该执行简单命令', async () => {
    const result = await executeShellCommand('echo "hello world"');
    expect(result.success).toBe(true);
    expect(result.stdout).toContain('hello world');
    expect(result.exitCode).toBe(0);
  });

  test('executeShellCommand - 应该处理失败的命令', async () => {
    const result = await executeShellCommand('non-existent-command-xyz');
    expect(result.success).toBe(false);
    expect(result.exitCode).not.toBe(0);
  });

  test('isCommandAvailable - 应该检查命令可用性', async () => {
    // 测试一个通常可用的命令
    const nodeAvailable = await isCommandAvailable('node');
    expect(typeof nodeAvailable).toBe('boolean');

    // 测试一个不存在的命令
    const fakeAvailable = await isCommandAvailable('non-existent-command-xyz-123');
    expect(fakeAvailable).toBe(false);
  }, 10000);

  test('buildMineruCommand - 应该构建正确的命令参数', () => {
    const args = buildMineruCommand('/input/file.pdf', '/output/dir', {
      method: 'auto',
      lang: 'en',
      backend: 'pipeline',
      startPage: 1,
      endPage: 10,
      formula: true,
      table: true,
      device: 'cpu',
      source: 'huggingface'
    });

    expect(args).toContain('-p');
    expect(args).toContain('/input/file.pdf');
    expect(args).toContain('-o');
    expect(args).toContain('/output/dir');
    expect(args).toContain('-m');
    expect(args).toContain('auto');
    expect(args).toContain('-l');
    expect(args).toContain('en');
    expect(args).toContain('-s');
    expect(args).toContain('1');
    expect(args).toContain('-e');
    expect(args).toContain('10');
    expect(args).toContain('--device');
    expect(args).toContain('cpu');
  });
});

describe('提示词工具', () => {
  test('replacePromptVariables - 应该替换变量', () => {
    const template = 'Hello {name}, your age is {age}';
    const variables = { name: 'John', age: '25' };
    const result = replacePromptVariables(template, variables);
    
    expect(result).toBe('Hello John, your age is 25');
  });

  test('replacePromptVariables - 应该处理缺失的变量', () => {
    const template = 'Hello {name}, your age is {age}';
    const variables = { name: 'John' }; // 缺少age
    const result = replacePromptVariables(template, variables);
    
    expect(result).toBe('Hello John, your age is {age}');
  });

  test('buildPrompt - 应该构建完整提示词', () => {
    const result = buildPrompt('IMAGE_ANALYSIS', {
      image_description: 'test image',
      image_caption: 'test caption',
      image_footnote: 'test footnote'
    });

    expect(result).toHaveProperty('user');
    expect(result).toHaveProperty('system');
    expect(typeof result.user).toBe('string');
    expect(typeof result.system).toBe('string');
    expect(result.user).toContain('test image');
  });

  test('buildPrompt - 应该处理不存在的模板', () => {
    expect(() => {
      buildPrompt('NON_EXISTENT_TEMPLATE' as any, {});
    }).toThrow();
  });
});

describe('日志工具', () => {
  test('createLogger - 应该创建日志记录器', () => {
    const logger = createLogger('test-logger');
    expect(logger).toBeDefined();
    expect(typeof logger.info).toBe('function');
    expect(typeof logger.error).toBe('function');
    expect(typeof logger.warn).toBe('function');
    expect(typeof logger.debug).toBe('function');
  });

  test('getModuleLogger - 应该创建模块日志记录器', () => {
    const logger = getModuleLogger('test-module');
    expect(logger).toBeDefined();
    expect(typeof logger.info).toBe('function');
  });

  test('日志记录器应该正常工作', () => {
    const logger = createLogger('test-logger', { level: 'debug' });
    
    // 这些调用不应该抛出错误
    expect(() => {
      logger.info('Test info message');
      logger.warn('Test warning message');
      logger.error('Test error message');
      logger.debug('Test debug message');
    }).not.toThrow();
  });
});
