/**
 * LightRAG兼容的RAG后端实现
 * 模拟LightRAG的核心功能和接口
 */

import fs from 'fs-extra';
import path from 'path';
import crypto from 'crypto';
import { 
  RAGBackend, 
  EntityInfo, 
  ChunkData, 
  QueryParams, 
  QueryResult,
  RelationshipInfo,
  NodeData,
  EmbeddingFunction,
  LLMModelFunction
} from '@/types';
import { getModuleLogger } from '@/utils/logger';
import { calculateStringHash } from '@/utils/fileUtils';

const logger = getModuleLogger('LightRAGBackend');

/**
 * 文本块存储接口
 */
interface TextChunkStorage {
  upsert(data: Record<string, ChunkData>): Promise<void>;
  get(id: string): Promise<ChunkData | null>;
  delete(ids: string[]): Promise<void>;
}

/**
 * 向量数据库接口
 */
interface VectorDatabase {
  upsert(data: Record<string, any>): Promise<void>;
  search(query: string, topK?: number): Promise<any[]>;
  delete(ids: string[]): Promise<void>;
}

/**
 * 知识图谱接口
 */
interface KnowledgeGraph {
  upsertNode(nodeId: string, nodeData: NodeData): Promise<void>;
  upsertEdge(sourceId: string, targetId: string, edgeData: any): Promise<void>;
  getNode(nodeId: string): Promise<NodeData | null>;
  getNeighbors(nodeId: string): Promise<string[]>;
  removeNode(nodeId: string): Promise<void>;
  removeEdge(sourceId: string, targetId: string): Promise<void>;
}

/**
 * LightRAG兼容的文本块存储实现
 */
class LightRAGTextChunkStorage implements TextChunkStorage {
  private chunks: Map<string, ChunkData> = new Map();
  private persistPath: string;

  constructor(workingDir: string) {
    this.persistPath = path.join(workingDir, 'text_chunks.json');
  }

  async upsert(data: Record<string, ChunkData>): Promise<void> {
    for (const [id, chunk] of Object.entries(data)) {
      this.chunks.set(id, chunk);
    }
    await this.persist();
  }

  async get(id: string): Promise<ChunkData | null> {
    return this.chunks.get(id) || null;
  }

  async delete(ids: string[]): Promise<void> {
    for (const id of ids) {
      this.chunks.delete(id);
    }
    await this.persist();
  }

  async load(): Promise<void> {
    try {
      if (await fs.pathExists(this.persistPath)) {
        const data = await fs.readJson(this.persistPath);
        this.chunks = new Map(Object.entries(data));
      }
    } catch (error) {
      logger.warn('Failed to load text chunks:', error);
    }
  }

  private async persist(): Promise<void> {
    try {
      const data = Object.fromEntries(this.chunks);
      await fs.writeJson(this.persistPath, data, { spaces: 2 });
    } catch (error) {
      logger.warn('Failed to persist text chunks:', error);
    }
  }

  /**
   * 索引完成回调
   */
  async indexDoneCallback(): Promise<void> {
    // 执行索引完成后的操作，如保存数据
    await this.persist();
    logger.debug('Text chunks index done callback completed');
  }
}

/**
 * LightRAG兼容的向量数据库实现
 */
class LightRAGVectorDatabase implements VectorDatabase {
  private data: Map<string, any> = new Map();
  private embeddings: Map<string, number[]> = new Map();
  private embeddingFunc: EmbeddingFunction | undefined;
  private persistPath: string;

  constructor(workingDir: string, embeddingFunc?: EmbeddingFunction) {
    this.embeddingFunc = embeddingFunc;
    this.persistPath = path.join(workingDir, 'vector_db.json');
  }

  async upsert(data: Record<string, any>): Promise<void> {
    for (const [id, item] of Object.entries(data)) {
      this.data.set(id, item);
      
      // 计算向量
      if (this.embeddingFunc && item.content) {
        try {
          const embeddings = await this.embeddingFunc([item.content]);
          if (embeddings.length > 0 && embeddings[0]) {
            this.embeddings.set(id, embeddings[0]);
          }
        } catch (error) {
          logger.warn(`Failed to compute embedding for ${id}:`, error);
        }
      }
    }
    await this.persist();
  }

  async search(query: string, topK: number = 5): Promise<any[]> {
    if (!this.embeddingFunc) {
      // 简单文本匹配
      const results: any[] = [];
      const queryLower = query.toLowerCase();
      
      for (const [id, item] of this.data.entries()) {
        if (item.content && item.content.toLowerCase().includes(queryLower)) {
          results.push({ id, ...item, score: 1.0 });
        }
      }
      
      return results.slice(0, topK);
    }

    try {
      const queryEmbeddings = await this.embeddingFunc([query]);
      if (queryEmbeddings.length === 0 || !queryEmbeddings[0]) {
        return [];
      }

      const queryVector = queryEmbeddings[0];
      const results: Array<{ id: string; item: any; score: number }> = [];

      for (const [id, embedding] of this.embeddings.entries()) {
        if (embedding) {
          const similarity = this.cosineSimilarity(queryVector, embedding);
          const item = this.data.get(id);
          if (item) {
            results.push({ id, item, score: similarity });
          }
        }
      }

      results.sort((a, b) => b.score - a.score);
      return results.slice(0, topK).map(r => ({ id: r.id, ...r.item, score: r.score }));

    } catch (error) {
      logger.error('Vector search failed:', error);
      return [];
    }
  }

  async delete(ids: string[]): Promise<void> {
    for (const id of ids) {
      this.data.delete(id);
      this.embeddings.delete(id);
    }
    await this.persist();
  }

  async load(): Promise<void> {
    try {
      if (await fs.pathExists(this.persistPath)) {
        const data = await fs.readJson(this.persistPath);
        this.data = new Map(Object.entries(data.data || {}));
        this.embeddings = new Map(Object.entries(data.embeddings || {}));
      }
    } catch (error) {
      logger.warn('Failed to load vector database:', error);
    }
  }

  private async persist(): Promise<void> {
    try {
      const data = {
        data: Object.fromEntries(this.data),
        embeddings: Object.fromEntries(this.embeddings),
        timestamp: Date.now()
      };
      await fs.writeJson(this.persistPath, data, { spaces: 2 });
    } catch (error) {
      logger.warn('Failed to persist vector database:', error);
    }
  }

  private cosineSimilarity(a: number[], b: number[]): number {
    if (!a || !b || a.length !== b.length) return 0;

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < a.length; i++) {
      const aVal = a[i] || 0;
      const bVal = b[i] || 0;
      dotProduct += aVal * bVal;
      normA += aVal * aVal;
      normB += bVal * bVal;
    }

    const denominator = Math.sqrt(normA) * Math.sqrt(normB);
    return denominator === 0 ? 0 : dotProduct / denominator;
  }

  /**
   * 索引完成回调
   */
  async indexDoneCallback(): Promise<void> {
    // 执行索引完成后的操作，如保存数据
    await this.persist();
    logger.debug('Vector database index done callback completed');
  }
}

/**
 * LightRAG兼容的知识图谱实现
 */
class LightRAGKnowledgeGraph implements KnowledgeGraph {
  private nodes: Map<string, NodeData> = new Map();
  private edges: Map<string, Map<string, any>> = new Map();
  private persistPath: string;

  constructor(workingDir: string) {
    this.persistPath = path.join(workingDir, 'knowledge_graph.json');
  }

  async upsertNode(nodeId: string, nodeData: NodeData): Promise<void> {
    this.nodes.set(nodeId, nodeData);
    if (!this.edges.has(nodeId)) {
      this.edges.set(nodeId, new Map());
    }
    await this.persist();
  }

  async upsertEdge(sourceId: string, targetId: string, edgeData: any): Promise<void> {
    if (!this.edges.has(sourceId)) {
      this.edges.set(sourceId, new Map());
    }
    if (!this.edges.has(targetId)) {
      this.edges.set(targetId, new Map());
    }
    
    this.edges.get(sourceId)!.set(targetId, edgeData);
    await this.persist();
  }

  async getNode(nodeId: string): Promise<NodeData | null> {
    return this.nodes.get(nodeId) || null;
  }

  async getNeighbors(nodeId: string): Promise<string[]> {
    const neighbors = this.edges.get(nodeId);
    return neighbors ? Array.from(neighbors.keys()) : [];
  }

  async removeNode(nodeId: string): Promise<void> {
    this.nodes.delete(nodeId);
    this.edges.delete(nodeId);
    
    // 删除指向该节点的边
    for (const [_, neighbors] of this.edges.entries()) {
      neighbors.delete(nodeId);
    }
    await this.persist();
  }

  async removeEdge(sourceId: string, targetId: string): Promise<void> {
    const neighbors = this.edges.get(sourceId);
    if (neighbors) {
      neighbors.delete(targetId);
    }
    await this.persist();
  }

  async load(): Promise<void> {
    try {
      if (await fs.pathExists(this.persistPath)) {
        const data = await fs.readJson(this.persistPath);
        this.nodes = new Map(Object.entries(data.nodes || {}));
        
        // 重建边的Map结构
        this.edges = new Map();
        for (const [sourceId, targets] of Object.entries(data.edges || {})) {
          this.edges.set(sourceId, new Map(Object.entries(targets as any)));
        }
      }
    } catch (error) {
      logger.warn('Failed to load knowledge graph:', error);
    }
  }

  private async persist(): Promise<void> {
    try {
      const data = {
        nodes: Object.fromEntries(this.nodes),
        edges: Object.fromEntries(
          Array.from(this.edges.entries()).map(([key, value]) => [
            key,
            Object.fromEntries(value)
          ])
        ),
        timestamp: Date.now()
      };
      await fs.writeJson(this.persistPath, data, { spaces: 2 });
    } catch (error) {
      logger.warn('Failed to persist knowledge graph:', error);
    }
  }

  /**
   * 索引完成回调
   */
  async indexDoneCallback(): Promise<void> {
    // 执行索引完成后的操作，如保存数据
    await this.persist();
    logger.debug('Knowledge graph index done callback completed');
  }
}

/**
 * LightRAG兼容的RAG后端实现
 */
export class LightRAGBackend implements RAGBackend {
  private workingDir: string;
  private textChunks: LightRAGTextChunkStorage;
  private chunksVdb: LightRAGVectorDatabase;
  private entitiesVdb: LightRAGVectorDatabase;
  private relationshipsVdb: LightRAGVectorDatabase;
  private knowledgeGraph: LightRAGKnowledgeGraph;
  private llmModelFunc: LLMModelFunction | undefined;
  private embeddingFunc: EmbeddingFunction | undefined;
  private initialized: boolean = false;

  constructor(
    workingDir: string,
    embeddingFunc?: EmbeddingFunction,
    llmModelFunc?: LLMModelFunction
  ) {
    this.workingDir = workingDir;
    this.embeddingFunc = embeddingFunc;
    this.llmModelFunc = llmModelFunc;
    
    // 初始化存储组件
    this.textChunks = new LightRAGTextChunkStorage(workingDir);
    this.chunksVdb = new LightRAGVectorDatabase(path.join(workingDir, 'chunks'), embeddingFunc);
    this.entitiesVdb = new LightRAGVectorDatabase(path.join(workingDir, 'entities'), embeddingFunc);
    this.relationshipsVdb = new LightRAGVectorDatabase(path.join(workingDir, 'relationships'), embeddingFunc);
    this.knowledgeGraph = new LightRAGKnowledgeGraph(workingDir);
  }

  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // 确保工作目录存在
      await fs.ensureDir(this.workingDir);
      await fs.ensureDir(path.join(this.workingDir, 'chunks'));
      await fs.ensureDir(path.join(this.workingDir, 'entities'));
      await fs.ensureDir(path.join(this.workingDir, 'relationships'));
      
      // 加载已存在的数据
      await Promise.all([
        this.textChunks.load(),
        this.chunksVdb.load(),
        this.entitiesVdb.load(),
        this.relationshipsVdb.load(),
        this.knowledgeGraph.load()
      ]);
      
      this.initialized = true;
      logger.info('LightRAGBackend initialized successfully');
      
    } catch (error) {
      logger.error('Failed to initialize LightRAGBackend:', error);
      throw error;
    }
  }

  // 实现其他RAGBackend接口方法...
  async insertTextContent(
    content: string,
    filePath: string,
    options: {
      splitByCharacter?: string;
      splitByCharacterOnly?: boolean;
      docId?: string;
    } = {}
  ): Promise<void> {
    try {
      logger.info(`Inserting text content from ${filePath}`);

      // 步骤1: 文本分块
      const chunks = this.splitTextContent(content, options);

      // 步骤2: 为每个文本块创建存储记录
      const chunkIds: string[] = [];
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        if (!chunk) continue; // 跳过空块

        const chunkId = options.docId ? `${options.docId}_chunk_${i}` : calculateStringHash(chunk, 'chunk-');

        const chunkData: ChunkData = {
          tokens: this.estimateTokens(chunk),
          content: chunk,
          chunk_order_index: i,
          full_doc_id: options.docId || calculateStringHash(content, 'doc-'),
          file_path: filePath
        };

        // 存储文本块
        await this.textChunks.upsert({ [chunkId]: chunkData });

        // 添加到向量数据库
        await this.chunksVdb.upsert({
          [chunkId]: {
            content: chunk,
            file_path: filePath,
            chunk_order_index: i,
            type: 'text'
          }
        });

        chunkIds.push(chunkId);
      }

      // 步骤3: 提取实体和关系（模拟LightRAG的extract_entities）
      await this.extractEntitiesAndRelationships(chunks, chunkIds, filePath);

      // 步骤4: 执行插入完成回调（模拟LightRAG的insert_done）
      await this.insertDoneCallback();

      logger.info(`Successfully inserted ${chunks.length} text chunks from ${filePath}`);

    } catch (error) {
      logger.error('Failed to insert text content:', error);
      throw error;
    }
  }

  /**
   * 插入完成回调（模拟LightRAG的insert_done回调机制）
   */
  private async insertDoneCallback(): Promise<void> {
    try {
      // 执行所有存储组件的索引完成回调
      await Promise.all([
        this.textChunks.indexDoneCallback?.() || Promise.resolve(),
        this.chunksVdb.indexDoneCallback?.() || Promise.resolve(),
        this.entitiesVdb.indexDoneCallback?.() || Promise.resolve(),
        this.relationshipsVdb.indexDoneCallback?.() || Promise.resolve(),
        this.knowledgeGraph.indexDoneCallback?.() || Promise.resolve()
      ]);

      logger.debug('Insert done callbacks completed');
    } catch (error) {
      logger.warn('Some insert done callbacks failed:', error);
    }
  }

  async createEntity(entityInfo: EntityInfo, chunkId: string): Promise<void> {
    try {
      const entityId = entityInfo.entity_name;

      // 创建节点数据
      const nodeData: NodeData = {
        entity_id: entityId,
        entity_type: entityInfo.entity_type,
        description: entityInfo.summary,
        source_id: chunkId,
        file_path: entityInfo.file_path || 'unknown',
        created_at: Date.now()
      };

      // 添加到知识图谱
      await this.knowledgeGraph.upsertNode(entityId, nodeData);

      // 添加到实体向量数据库
      await this.entitiesVdb.upsert({
        [calculateStringHash(entityId, 'ent-')]: {
          entity_name: entityId,
          entity_type: entityInfo.entity_type,
          content: `${entityId}\n${entityInfo.summary}`,
          source_id: chunkId,
          type: 'entity'
        }
      });

      logger.info(`Created entity: ${entityId}`);

    } catch (error) {
      logger.error('Failed to create entity:', error);
      throw error;
    }
  }

  async updateEntity(entityId: string, updates: Partial<EntityInfo>): Promise<void> {
    // 实现实体更新逻辑
    logger.info(`Updating entity: ${entityId}`);
  }

  async getEntity(entityId: string): Promise<EntityInfo | null> {
    // 实现实体获取逻辑
    return null;
  }

  async createRelationship(
    sourceEntityId: string,
    targetEntityId: string,
    relationshipType: string,
    description: string,
    metadata?: any
  ): Promise<void> {
    // 实现关系创建逻辑
    logger.info(`Creating relationship: ${sourceEntityId} -> ${targetEntityId} (${relationshipType})`);
  }

  async createTextChunk(chunkData: ChunkData): Promise<string> {
    // 实现文本块创建逻辑
    const chunkId = calculateStringHash(chunkData.content, 'chunk-');
    await this.textChunks.upsert({ [chunkId]: chunkData });
    return chunkId;
  }

  async getTextChunk(chunkId: string): Promise<ChunkData | null> {
    return await this.textChunks.get(chunkId);
  }

  async query(query: string, params: QueryParams = {}): Promise<QueryResult> {
    logger.info(`Processing LightRAG-style query: ${query}`);

    const { mode = 'hybrid', topK = 5 } = params;

    try {
      // 步骤1: 向量搜索相关文本块
      const chunkResults = await this.chunksVdb.search(query, topK);

      // 步骤2: 搜索相关实体
      const entityResults = await this.entitiesVdb.search(query, topK);

      // 步骤3: 搜索相关关系
      const relationshipResults = await this.relationshipsVdb.search(query, topK);

      // 步骤4: 根据查询模式组合结果
      let contextParts: string[] = [];
      let sources: any[] = [];

      if (mode === 'local' || mode === 'hybrid') {
        // 添加文本块上下文
        for (const result of chunkResults) {
          contextParts.push(`文本内容: ${result.content}`);
          sources.push({
            type: 'chunk',
            content: result.content,
            score: result.score,
            source_id: result.id
          });
        }
      }

      if (mode === 'global' || mode === 'hybrid') {
        // 添加实体上下文
        for (const result of entityResults) {
          contextParts.push(`实体信息: ${result.content}`);
          sources.push({
            type: 'entity',
            content: result.content,
            score: result.score,
            source_id: result.id
          });
        }

        // 添加关系上下文
        for (const result of relationshipResults) {
          contextParts.push(`关系信息: ${result.content}`);
          sources.push({
            type: 'relationship',
            content: result.content,
            score: result.score,
            source_id: result.id
          });
        }
      }

      // 步骤5: 如果没有找到相关内容
      if (contextParts.length === 0) {
        return {
          answer: '抱歉，没有找到相关信息。',
          sources: [],
          metadata: { mode, searchResults: 0 }
        };
      }

      // 步骤6: 使用LLM生成答案
      if (this.llmModelFunc) {
        const context = contextParts.join('\n\n');

        const systemPrompt = `你是一个专业的问答助手。请基于提供的上下文信息回答用户的问题。
如果上下文中没有足够的信息来回答问题，请诚实地说明。
请确保答案准确、有用且基于提供的信息。`;

        const userPrompt = `问题: ${query}

相关上下文信息:
${context}

请基于上述信息回答问题:`;

        try {
          const answer = await this.llmModelFunc(userPrompt, systemPrompt);

          return {
            answer: answer.trim(),
            sources,
            metadata: {
              mode,
              searchResults: contextParts.length,
              chunkResults: chunkResults.length,
              entityResults: entityResults.length,
              relationshipResults: relationshipResults.length
            }
          };
        } catch (error) {
          logger.error('Failed to generate answer with LLM:', error);
          // 降级到简单的上下文返回
          return {
            answer: `基于找到的信息:\n\n${context}`,
            sources,
            metadata: { mode, searchResults: contextParts.length, error: 'llm_failed' }
          };
        }
      } else {
        // 没有LLM函数时，直接返回上下文
        const context = contextParts.join('\n\n');
        return {
          answer: `找到以下相关信息:\n\n${context}`,
          sources,
          metadata: { mode, searchResults: contextParts.length }
        };
      }

    } catch (error) {
      logger.error('Query processing failed:', error);
      throw new Error(`Query failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async cleanup(): Promise<void> {
    logger.info('LightRAGBackend cleanup completed');
  }

  /**
   * 分割文本内容（模拟LightRAG的文本分块策略）
   */
  private splitTextContent(
    content: string,
    options: {
      splitByCharacter?: string;
      splitByCharacterOnly?: boolean;
    }
  ): string[] {
    const { splitByCharacter, splitByCharacterOnly = false } = options;
    const maxChunkTokens = 1200; // 模拟LightRAG的默认chunk_token_size

    // 如果指定了分割字符
    if (splitByCharacter) {
      const initialChunks = content.split(splitByCharacter).filter(chunk => chunk.trim().length > 0);

      if (splitByCharacterOnly) {
        // 仅按字符分割，不考虑token大小
        return initialChunks;
      } else {
        // 按字符分割后，检查token大小，如果超过限制则进一步分割
        const finalChunks: string[] = [];

        for (const chunk of initialChunks) {
          if (this.estimateTokenCount(chunk) <= maxChunkTokens) {
            finalChunks.push(chunk);
          } else {
            // 如果块太大，按token大小进一步分割
            const subChunks = this.splitByTokenSize(chunk, maxChunkTokens);
            finalChunks.push(...subChunks);
          }
        }

        return finalChunks;
      }
    }

    // 默认分块策略：按token大小分割
    return this.splitByTokenSize(content, maxChunkTokens);
  }

  /**
   * 按token大小分割文本（模拟LightRAG的token-based分块）
   */
  private splitByTokenSize(text: string, maxTokens: number): string[] {
    const chunks: string[] = [];

    // 首先按段落分割
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);

    let currentChunk = '';

    for (const paragraph of paragraphs) {
      const testChunk = currentChunk + (currentChunk ? '\n\n' : '') + paragraph;

      if (this.estimateTokenCount(testChunk) <= maxTokens) {
        currentChunk = testChunk;
      } else {
        // 如果当前块不为空，先保存它
        if (currentChunk.trim()) {
          chunks.push(currentChunk.trim());
        }

        // 检查单个段落是否超过限制
        if (this.estimateTokenCount(paragraph) > maxTokens) {
          // 如果单个段落太大，按句子分割
          const sentences = this.splitBySentences(paragraph, maxTokens);
          chunks.push(...sentences);
          currentChunk = '';
        } else {
          currentChunk = paragraph;
        }
      }
    }

    // 添加最后一个块
    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim());
    }

    return chunks.filter(chunk => chunk.length > 0);
  }

  /**
   * 按句子分割文本
   */
  private splitBySentences(text: string, maxTokens: number): string[] {
    const chunks: string[] = [];
    const sentences = text.split(/[.!?。！？]\s+/).filter(s => s.trim().length > 0);

    let currentChunk = '';

    for (const sentence of sentences) {
      const testChunk = currentChunk + (currentChunk ? '. ' : '') + sentence;

      if (this.estimateTokenCount(testChunk) <= maxTokens) {
        currentChunk = testChunk;
      } else {
        if (currentChunk.trim()) {
          chunks.push(currentChunk.trim());
        }

        // 如果单个句子仍然太大，强制分割
        if (this.estimateTokenCount(sentence) > maxTokens) {
          const words = sentence.split(/\s+/);
          let wordChunk = '';

          for (const word of words) {
            const testWordChunk = wordChunk + (wordChunk ? ' ' : '') + word;

            if (this.estimateTokenCount(testWordChunk) <= maxTokens) {
              wordChunk = testWordChunk;
            } else {
              if (wordChunk.trim()) {
                chunks.push(wordChunk.trim());
              }
              wordChunk = word;
            }
          }

          if (wordChunk.trim()) {
            chunks.push(wordChunk.trim());
          }

          currentChunk = '';
        } else {
          currentChunk = sentence;
        }
      }
    }

    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim());
    }

    return chunks.filter(chunk => chunk.length > 0);
  }

  /**
   * 估算token数量（模拟LightRAG的tokenizer）
   */
  private estimateTokenCount(text: string): number {
    // 简单的token估算：中文按字符数，英文按单词数，标点符号等
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = text.replace(/[\u4e00-\u9fff]/g, '').split(/\s+/).filter(w => w.length > 0).length;
    const punctuation = (text.match(/[.,!?;:()[\]{}"'`~@#$%^&*+=<>\/\\|_-]/g) || []).length;

    return chineseChars + englishWords + Math.ceil(punctuation / 2);
  }

  /**
   * 提取实体和关系（模拟LightRAG的extract_entities功能）
   */
  private async extractEntitiesAndRelationships(
    chunks: string[],
    chunkIds: string[],
    filePath: string
  ): Promise<void> {
    if (!this.llmModelFunc) {
      logger.warn('No LLM function available for entity extraction');
      return;
    }

    try {
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        const chunkId = chunkIds[i];

        if (!chunk || !chunkId) continue; // 跳过无效数据

        // 提取实体
        const entities = await this.extractEntitiesFromChunk(chunk, chunkId, filePath);

        // 提取关系
        await this.extractRelationshipsFromEntities(entities, chunk, chunkId, filePath);
      }

      // 执行实体和关系合并（模拟LightRAG的merge_nodes_and_edges）
      await this.mergeNodesAndEdges(chunks, chunkIds, filePath);

    } catch (error) {
      logger.error('Failed to extract entities and relationships:', error);
    }
  }

  /**
   * 合并节点和边（模拟LightRAG的merge_nodes_and_edges功能）
   */
  private async mergeNodesAndEdges(
    chunks: string[],
    chunkIds: string[],
    filePath: string
  ): Promise<void> {
    try {
      logger.debug('Starting nodes and edges merging process');

      // 收集所有实体和关系信息
      const allEntities = new Map<string, EntityInfo>();
      const allRelationships = new Map<string, any>();

      // 从向量数据库中获取相关实体
      for (const chunkId of chunkIds) {
        // 这里应该查询与chunk相关的实体
        // 简化实现：假设实体已经存储在entitiesVdb中
      }

      // 执行实体合并逻辑
      await this.mergeEntities(allEntities);

      // 执行关系合并逻辑
      await this.mergeRelationships(allRelationships);

      logger.debug('Nodes and edges merging completed');

    } catch (error) {
      logger.warn('Failed to merge nodes and edges:', error);
    }
  }

  /**
   * 合并相似实体
   */
  private async mergeEntities(entities: Map<string, EntityInfo>): Promise<void> {
    // 简化的实体合并逻辑
    // 在实际的LightRAG中，这会涉及复杂的实体消歧和合并算法

    const entityNames = Array.from(entities.keys()).filter(name => name); // 过滤空名称
    const mergedEntities = new Set<string>();

    for (let i = 0; i < entityNames.length; i++) {
      const entityA = entityNames[i];
      if (!entityA || mergedEntities.has(entityA)) continue;

      for (let j = i + 1; j < entityNames.length; j++) {
        const entityB = entityNames[j];
        if (!entityB || mergedEntities.has(entityB)) continue;

        // 简单的相似性检查（实际实现会更复杂）
        if (this.areEntitiesSimilar(entityA, entityB)) {
          // 合并实体B到实体A
          await this.mergeEntityPair(entityA, entityB, entities);
          mergedEntities.add(entityB);
        }
      }
    }
  }

  /**
   * 检查两个实体是否相似
   */
  private areEntitiesSimilar(entityA: string, entityB: string): boolean {
    // 简化的相似性检查
    const normalizedA = entityA.toLowerCase().trim();
    const normalizedB = entityB.toLowerCase().trim();

    // 完全匹配
    if (normalizedA === normalizedB) return true;

    // 包含关系
    if (normalizedA.includes(normalizedB) || normalizedB.includes(normalizedA)) {
      return true;
    }

    // 编辑距离检查（简化版）
    const distance = this.levenshteinDistance(normalizedA, normalizedB);
    const maxLength = Math.max(normalizedA.length, normalizedB.length);
    const similarity = 1 - distance / maxLength;

    return similarity > 0.8; // 80%相似度阈值
  }

  /**
   * 计算编辑距离
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix: number[][] = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(0));

    for (let i = 0; i <= str1.length; i++) matrix[0]![i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j]![0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j]![i] = Math.min(
          matrix[j]![i - 1]! + 1,
          matrix[j - 1]![i]! + 1,
          matrix[j - 1]![i - 1]! + indicator
        );
      }
    }

    return matrix[str2.length]![str1.length]!;
  }

  /**
   * 合并一对实体
   */
  private async mergeEntityPair(
    primaryEntity: string,
    secondaryEntity: string,
    entities: Map<string, EntityInfo>
  ): Promise<void> {
    try {
      const primaryInfo = entities.get(primaryEntity);
      const secondaryInfo = entities.get(secondaryEntity);

      if (!primaryInfo || !secondaryInfo) return;

      // 合并实体信息（保留主要实体，合并描述）
      const mergedSummary = `${primaryInfo.summary}; ${secondaryInfo.summary}`;

      const updatedEntity: EntityInfo = {
        ...primaryInfo,
        summary: mergedSummary,
        created_at: Math.min(primaryInfo.created_at || 0, secondaryInfo.created_at || 0)
      };

      // 更新实体信息
      await this.createEntity(updatedEntity, primaryInfo.source_id || 'unknown');

      // 更新所有指向次要实体的关系，使其指向主要实体
      await this.redirectEntityRelationships(secondaryEntity, primaryEntity);

      logger.debug(`Merged entity ${secondaryEntity} into ${primaryEntity}`);

    } catch (error) {
      logger.warn(`Failed to merge entities ${primaryEntity} and ${secondaryEntity}:`, error);
    }
  }

  /**
   * 重定向实体关系
   */
  private async redirectEntityRelationships(
    oldEntity: string,
    newEntity: string
  ): Promise<void> {
    // 这里应该更新知识图谱中的所有关系
    // 将指向oldEntity的关系重定向到newEntity
    // 简化实现
    logger.debug(`Redirecting relationships from ${oldEntity} to ${newEntity}`);
  }

  /**
   * 合并关系
   */
  private async mergeRelationships(relationships: Map<string, any>): Promise<void> {
    // 简化的关系合并逻辑
    // 在实际的LightRAG中，这会涉及关系去重和权重合并
    logger.debug('Merging relationships');
  }

  /**
   * 从文本块中提取实体
   */
  private async extractEntitiesFromChunk(
    chunk: string,
    chunkId: string,
    filePath: string
  ): Promise<EntityInfo[]> {
    try {
      const extractionPrompt = `
分析以下文本，提取其中的所有重要实体（人物、地点、组织、概念、事件等）：

文本内容：
${chunk}

请以JSON格式返回提取的实体：
{
  "entities": [
    {
      "name": "实体名称",
      "type": "实体类型（Person/Location/Organization/Concept/Event等）",
      "description": "实体的详细描述"
    }
  ]
}
`;

      const response = await this.llmModelFunc!(extractionPrompt);
      const extractedData = this.parseEntityExtractionResponse(response);

      const entities: EntityInfo[] = [];

      for (const entityData of extractedData.entities) {
        const entity: EntityInfo = {
          entity_name: entityData.name,
          entity_type: entityData.type,
          summary: entityData.description,
          source_id: chunkId,
          file_path: filePath,
          created_at: Date.now()
        };

        // 创建实体
        await this.createEntity(entity, chunkId);
        entities.push(entity);
      }

      return entities;

    } catch (error) {
      logger.warn('Failed to extract entities from chunk:', error);
      return [];
    }
  }

  /**
   * 从实体中提取关系
   */
  private async extractRelationshipsFromEntities(
    entities: EntityInfo[],
    chunk: string,
    chunkId: string,
    filePath: string
  ): Promise<void> {
    if (entities.length < 2) return;

    try {
      // 构建关系提取提示词
      const entityNames = entities.map(e => e.entity_name);
      const relationshipPrompt = `
分析以下文本中实体之间的关系：

文本内容：
${chunk}

实体列表：
${entityNames.join(', ')}

请识别这些实体之间的关系，以JSON格式返回：
{
  "relationships": [
    {
      "source": "源实体名称",
      "target": "目标实体名称",
      "type": "关系类型（如：works_at, located_in, part_of, related_to等）",
      "description": "关系描述"
    }
  ]
}
`;

      const response = await this.llmModelFunc!(relationshipPrompt);
      const relationshipData = this.parseRelationshipExtractionResponse(response);

      // 创建关系
      for (const rel of relationshipData.relationships) {
        try {
          await this.createRelationship(
            rel.source,
            rel.target,
            rel.type,
            rel.description,
            {
              source_chunk: chunkId,
              file_path: filePath,
              auto_extracted: true,
              weight: 1.0
            }
          );
        } catch (error) {
          logger.warn(`Failed to create relationship ${rel.source} -> ${rel.target}:`, error);
        }
      }

    } catch (error) {
      logger.warn('Failed to extract relationships:', error);
    }
  }

  /**
   * 解析实体提取响应
   */
  private parseEntityExtractionResponse(response: string): { entities: Array<{ name: string; type: string; description: string }> } {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          entities: parsed.entities || []
        };
      }
    } catch (error) {
      logger.warn('Failed to parse entity extraction response:', error);
    }

    return { entities: [] };
  }

  /**
   * 解析关系提取响应
   */
  private parseRelationshipExtractionResponse(response: string): { relationships: Array<{ source: string; target: string; type: string; description: string }> } {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          relationships: parsed.relationships || []
        };
      }
    } catch (error) {
      logger.warn('Failed to parse relationship extraction response:', error);
    }

    return { relationships: [] };
  }

  /**
   * 估算token数量
   */
  private estimateTokens(text: string): number {
    // 简单的token估算：平均每个token约4个字符
    return Math.ceil(text.length / 4);
  }
}
