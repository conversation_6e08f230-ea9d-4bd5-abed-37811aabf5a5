#!/usr/bin/env node

/**
 * 构建脚本
 */

const { execSync } = require('child_process');
const fs = require('fs-extra');
const path = require('path');

async function main() {
  console.log('🚀 开始构建 RAG-Anything TypeScript...');

  try {
    // 清理旧的构建文件
    console.log('📁 清理构建目录...');
    await fs.remove('./dist');

    // 编译TypeScript
    console.log('🔨 编译TypeScript...');
    execSync('npx tsc', { stdio: 'inherit' });

    // 解析路径别名
    console.log('🔗 解析路径别名...');
    execSync('npx tsc-alias', { stdio: 'inherit' });

    // 生成单个类型声明文件
    console.log('📝 生成单个类型声明文件...');
    await generateSingleDeclarationFile();

    // 复制非TypeScript文件
    console.log('📋 复制资源文件...');
    await copyAssets();

    // 生成package.json用于发布
    console.log('📦 生成发布配置...');
    await generateDistPackageJson();

    console.log('✅ 构建完成！');
    console.log('📂 构建文件位于: ./dist');

  } catch (error) {
    console.error('❌ 构建失败:', error.message);
    process.exit(1);
  }
}

async function generateSingleDeclarationFile() {
  try {
    // 方法1: 尝试使用dts-bundle-generator配置文件
    try {
      execSync('npx dts-bundle-generator --config ./dts-bundle-generator.config.js', { stdio: 'inherit' });
      console.log('  ✓ 使用 dts-bundle-generator 生成单个类型声明文件');

      // 添加自定义头部注释
      await addCustomHeader();
      return;
    } catch (error) {
      console.log('  ⚠️ dts-bundle-generator 失败，尝试备用方法...');
    }

    // 方法2: 尝试直接命令行方式
    try {
      execSync('npx dts-bundle-generator -o ./dist/index.d.ts ./src/index.ts --no-check=false --export-referenced-types=true', { stdio: 'inherit' });
      console.log('  ✓ 使用 dts-bundle-generator 命令行生成单个类型声明文件');

      // 添加自定义头部注释
      await addCustomHeader();
      return;
    } catch (error) {
      console.log('  ⚠️ dts-bundle-generator 命令行也失败，尝试手动合并...');
    }

    // 方法3: 手动合并类型声明文件
    await manuallyMergeDeclarationFiles();

  } catch (error) {
    console.warn('  ⚠️ 生成单个类型声明文件失败，保持原有的多文件结构:', error.message);
  }
}

async function addCustomHeader() {
  const indexDtsPath = './dist/index.d.ts';

  if (await fs.pathExists(indexDtsPath)) {
    const content = await fs.readFile(indexDtsPath, 'utf8');

    const header = `/**
 * RAG-Anything TypeScript - 类型声明文件
 *
 * 这是一个自动生成的合并类型声明文件，包含了整个RAG-Anything库的所有类型定义。
 *
 * @version 1.0.0
 * <AUTHOR> Guo (TypeScript port)
 * @description RAG-Anything: All-in-One RAG System - TypeScript Implementation
 *
 * 主要功能模块:
 * - 核心RAG系统 (RAGAnything)
 * - 多模态处理器 (ImageModalProcessor, TableModalProcessor, etc.)
 * - 文档解析器 (MineruParser)
 * - 工具函数 (logger, fileUtils, commandUtils, etc.)
 * - OpenAI集成 (createOpenAILLMFunction, etc.)
 *
 * 使用方法:
 * import { RAGAnything, ImageModalProcessor } from 'rag-anything-ts';
 *
 * 自动生成时间: ${new Date().toISOString()}
 */

${content}`;

    await fs.writeFile(indexDtsPath, header, 'utf8');
    console.log('  ✓ 添加自定义头部注释');
  }
}

async function manuallyMergeDeclarationFiles() {
  const distPath = './dist';
  const indexDtsPath = path.join(distPath, 'index.d.ts');

  // 读取主入口的类型声明文件
  let mainContent = await fs.readFile(indexDtsPath, 'utf8');

  // 收集所有需要合并的类型声明文件
  const declarationFiles = await collectDeclarationFiles(distPath);

  // 创建合并后的内容
  let mergedContent = `/**
 * RAG-Anything TypeScript - 合并的类型声明文件
 * 自动生成，请勿手动修改
 */

`;

  // 添加所有类型定义
  const processedFiles = new Set();

  for (const filePath of declarationFiles) {
    if (filePath === indexDtsPath || processedFiles.has(filePath)) {
      continue;
    }

    const relativePath = path.relative(distPath, filePath);
    const content = await fs.readFile(filePath, 'utf8');

    // 移除import/export语句，保留类型定义
    const cleanContent = cleanDeclarationContent(content, relativePath);

    if (cleanContent.trim()) {
      mergedContent += `// ===== ${relativePath} =====\n`;
      mergedContent += cleanContent;
      mergedContent += '\n\n';
    }

    processedFiles.add(filePath);
  }

  // 添加主入口的导出
  mergedContent += `// ===== 主入口导出 =====\n`;
  mergedContent += cleanMainIndexContent(mainContent);

  // 写入合并后的文件
  await fs.writeFile(indexDtsPath, mergedContent, 'utf8');
  console.log('  ✓ 手动合并类型声明文件完成');
}

async function collectDeclarationFiles(dir) {
  const files = [];

  async function walk(currentDir) {
    const entries = await fs.readdir(currentDir, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(currentDir, entry.name);

      if (entry.isDirectory()) {
        await walk(fullPath);
      } else if (entry.name.endsWith('.d.ts') && !entry.name.endsWith('.d.ts.map')) {
        files.push(fullPath);
      }
    }
  }

  await walk(dir);
  return files;
}

function cleanDeclarationContent(content, filePath) {
  // 移除import语句
  content = content.replace(/^import\s+.*?;?\s*$/gm, '');

  // 移除相对路径的export from语句
  content = content.replace(/^export\s+.*?from\s+['"]\..*?['"];?\s*$/gm, '');

  // 保留类型导出和声明
  return content.trim();
}

function cleanMainIndexContent(content) {
  // 转换相对导入为直接导出
  content = content.replace(/export\s+\{([^}]+)\}\s+from\s+['"]\.\/([^'"]+)['"];?/g,
    (match, exports, path) => {
      return `// Re-exported from ${path}\n${match}`;
    });

  return content;
}

async function copyAssets() {
  const assetsToMove = [
    { src: './README.md', dest: './dist/README.md' },
    { src: './LICENSE', dest: './dist/LICENSE' }
  ];

  for (const asset of assetsToMove) {
    if (await fs.pathExists(asset.src)) {
      await fs.copy(asset.src, asset.dest);
      console.log(`  ✓ 复制 ${asset.src} -> ${asset.dest}`);
    }
  }
}

async function generateDistPackageJson() {
  const packageJson = await fs.readJson('./package.json');
  
  // 创建用于发布的package.json
  const distPackageJson = {
    name: packageJson.name,
    version: packageJson.version,
    description: packageJson.description,
    main: 'index.js',
    types: 'index.d.ts',
    keywords: packageJson.keywords,
    author: packageJson.author,
    license: packageJson.license,
    repository: packageJson.repository,
    dependencies: packageJson.dependencies,
    engines: packageJson.engines,
    files: ['**/*']
  };

  await fs.writeJson('./dist/package.json', distPackageJson, { spaces: 2 });
  console.log('  ✓ 生成 ./dist/package.json');
}

if (require.main === module) {
  main();
}
