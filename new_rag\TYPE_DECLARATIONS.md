# RAG-Anything TypeScript 类型声明文件

本项目在构建时会自动生成一个统一的类型声明文件 `dist/index.d.ts`，包含了整个RAG-Anything库的所有类型定义。

## 📁 文件结构

```
dist/
├── index.js          # 编译后的JavaScript代码
├── index.d.ts        # 统一的类型声明文件 ⭐
├── index.d.ts.map    # 类型声明映射文件
└── package.json      # 发布配置
```

## 🔧 构建配置

### 自动生成

项目使用 `dts-bundle-generator` 工具自动将所有分散的 `.d.ts` 文件合并为单个文件：

```bash
# 完整构建（包含类型声明文件生成）
npm run build

# 仅生成类型声明文件
npm run build:types
```

### 构建流程

1. **TypeScript编译**: 使用 `tsc` 编译源代码并生成分散的 `.d.ts` 文件
2. **路径别名解析**: 使用 `tsc-alias` 解析路径别名
3. **类型声明合并**: 使用 `dts-bundle-generator` 将所有类型声明合并为单个文件
4. **添加头部注释**: 自动添加包含版本信息和使用说明的头部注释

## 📋 包含的类型定义

生成的 `index.d.ts` 文件包含以下主要类型：

### 核心类型
- `ContentType` - 内容类型枚举
- `ParseMethod` - 解析方法枚举  
- `QueryMode` - 查询模式枚举
- `ProcessorType` - 处理器类型枚举

### 接口定义
- `ContentBlock` - 文档内容块接口
- `ImageContent` - 图像内容接口
- `TableContent` - 表格内容接口
- `EquationContent` - 公式内容接口
- `TextContent` - 文本内容接口
- `GenericContent` - 通用内容接口
- `ParseResult` - 解析结果接口
- `EntityInfo` - 实体信息接口
- `RAGAnythingConfig` - RAG配置接口
- `OpenAIConfig` - OpenAI配置接口

### 函数类型
- `LLMModelFunction` - LLM模型函数类型
- `VisionModelFunction` - 视觉模型函数类型
- `EmbeddingFunction` - 嵌入函数类型

### 类声明
- `RAGAnything` - 核心RAG系统类
- `DefaultRAGBackend` - 默认RAG后端类
- `MineruParser` - MinerU解析器类
- `BaseModalProcessor` - 基础模态处理器类
- `ImageModalProcessor` - 图像处理器类
- `TableModalProcessor` - 表格处理器类
- `EquationModalProcessor` - 公式处理器类
- `GenericModalProcessor` - 通用处理器类

### 工具函数
- 日志工具: `createLogger`, `getModuleLogger`, `setLogLevel`
- 文件工具: `fileExists`, `ensureDir`, `calculateStringHash` 等
- 命令工具: `executeCommand`, `checkMineruInstallation` 等
- 提示词工具: `PROMPTS`, `replacePromptVariables`, `buildPrompt`
- OpenAI工具: `createOpenAILLMFunction`, `createOpenAIVisionFunction` 等

## 💡 使用方法

### 基本导入

```typescript
import { 
  RAGAnything, 
  ImageModalProcessor, 
  ContentType,
  RAGAnythingConfig 
} from 'rag-anything-ts';

// 类型会自动从 dist/index.d.ts 中加载
const config: RAGAnythingConfig = {
  workingDir: './storage',
  autoInitializeBackend: true
};

const rag = new RAGAnything(config);
```

### 类型检查

由于所有类型都在单个文件中，TypeScript编译器可以：
- 更快地进行类型检查
- 提供更好的智能提示
- 减少类型解析时间
- 避免循环依赖问题

### 在其他项目中使用

```typescript
// 安装包后，类型会自动可用
import { RAGAnything, OpenAIConfig } from 'rag-anything-ts';

const openaiConfig: OpenAIConfig = {
  apiKey: process.env.OPENAI_API_KEY!,
  model: 'gpt-4',
  baseURL: 'https://api.openai.com/v1'
};
```

## 🛠️ 自定义配置

### dts-bundle-generator 配置

项目包含 `dts-bundle-generator.config.js` 配置文件：

```javascript
module.exports = {
  entries: [
    {
      filePath: './src/index.ts',
      outFile: './dist/index.d.ts',
      noCheck: false,
      output: {
        noBanner: false,
        respectPreserveConstEnum: true,
        exportReferencedTypes: true,
        sortNodes: true
      }
    }
  ]
};
```

### 构建脚本配置

在 `scripts/build.js` 中配置了多种生成方法：

1. **优先方法**: 使用配置文件的 `dts-bundle-generator`
2. **备用方法**: 使用命令行的 `dts-bundle-generator`  
3. **降级方法**: 手动合并类型声明文件

## 📊 文件信息

- **文件大小**: 约 28KB
- **包含行数**: 约 1,147 行
- **生成时间**: 构建时自动生成
- **更新频率**: 每次构建时重新生成

## 🔍 验证测试

项目包含 `test-types.ts` 文件用于验证类型声明文件的正确性：

```bash
# 运行类型验证测试
npx ts-node test-types.ts
```

测试内容包括：
- 所有主要类型的导入和使用
- 类的实例化测试
- 函数类型的验证
- 接口的类型检查

## 📝 注意事项

1. **自动生成**: `dist/index.d.ts` 是自动生成的文件，请勿手动修改
2. **构建依赖**: 需要安装 `dts-bundle-generator` 依赖
3. **版本同步**: 类型声明文件会自动包含版本信息和生成时间
4. **兼容性**: 生成的类型声明文件兼容 TypeScript 4.0+

## 🚀 发布

构建后的 `dist/` 目录包含：
- 编译后的JavaScript代码
- 统一的类型声明文件
- 发布用的package.json

可以直接发布到npm或其他包管理器。
