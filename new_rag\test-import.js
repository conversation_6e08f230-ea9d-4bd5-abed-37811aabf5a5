/**
 * 测试导入脚本
 * 验证生成的CommonJS和UMD文件是否可以正常导入
 */

console.log('🧪 测试模块导入...\n');

// 测试CommonJS导入
try {
  console.log('📦 测试CommonJS导入...');
  
  // 只导入不依赖sharp的核心模块
  const cjsModule = require('./dist/index.js');
  
  // 检查主要导出
  const expectedExports = [
    'RAGAnything',
    'DefaultRAGBackend', 
    'MineruParser',
    'BaseModalProcessor',
    'createLogger',
    'fileExists',
    'VERSION',
    'AUTHOR'
  ];
  
  const availableExports = Object.keys(cjsModule);
  console.log(`   ✓ 总导出数量: ${availableExports.length}`);
  
  const missingExports = expectedExports.filter(exp => !availableExports.includes(exp));
  const foundExports = expectedExports.filter(exp => availableExports.includes(exp));
  
  console.log(`   ✓ 找到的主要导出 (${foundExports.length}/${expectedExports.length}): ${foundExports.join(', ')}`);
  
  if (missingExports.length > 0) {
    console.log(`   ⚠️ 缺少的导出: ${missingExports.join(', ')}`);
  }
  
  // 测试基本功能
  if (cjsModule.VERSION) {
    console.log(`   ✓ 版本信息: ${cjsModule.VERSION}`);
  }
  
  if (cjsModule.AUTHOR) {
    console.log(`   ✓ 作者信息: ${cjsModule.AUTHOR}`);
  }
  
  // 测试类实例化（不涉及sharp）
  if (cjsModule.RAGAnything) {
    try {
      const rag = new cjsModule.RAGAnything({
        workingDir: './test_temp'
      });
      console.log(`   ✓ RAGAnything类实例化成功`);
    } catch (error) {
      console.log(`   ⚠️ RAGAnything实例化失败: ${error.message}`);
    }
  }
  
  console.log('   ✅ CommonJS导入测试完成\n');
  
} catch (error) {
  console.error(`   ❌ CommonJS导入失败: ${error.message}\n`);
}

// 测试类型声明文件
try {
  console.log('📝 测试类型声明文件...');
  
  const fs = require('fs');
  const dtsContent = fs.readFileSync('./dist/index.d.ts', 'utf8');
  
  // 检查关键类型定义
  const typeChecks = [
    { name: 'RAGAnything类', pattern: /export declare class RAGAnything/ },
    { name: 'BaseModalProcessor类', pattern: /export declare abstract class BaseModalProcessor/ },
    { name: 'ContentType类型', pattern: /export type ContentType/ },
    { name: 'LLMModelFunction类型', pattern: /export type LLMModelFunction/ },
    { name: 'RAGAnythingConfig接口', pattern: /export interface RAGAnythingConfig/ }
  ];
  
  let passedChecks = 0;
  typeChecks.forEach(check => {
    if (check.pattern.test(dtsContent)) {
      console.log(`   ✓ ${check.name}: 已包含`);
      passedChecks++;
    } else {
      console.log(`   ⚠️ ${check.name}: 未找到`);
    }
  });
  
  console.log(`   ✅ 类型检查完成 (${passedChecks}/${typeChecks.length})\n`);
  
} catch (error) {
  console.error(`   ❌ 类型声明文件检查失败: ${error.message}\n`);
}

// 文件大小统计
try {
  console.log('📊 文件统计信息...');
  
  const fs = require('fs');
  const files = [
    { name: 'CommonJS (index.js)', path: './dist/index.js' },
    { name: 'UMD (index.umd.js)', path: './dist/index.umd.js' },
    { name: '类型声明 (index.d.ts)', path: './dist/index.d.ts' }
  ];
  
  let totalSize = 0;
  files.forEach(file => {
    if (fs.existsSync(file.path)) {
      const stats = fs.statSync(file.path);
      const sizeKB = (stats.size / 1024).toFixed(1);
      console.log(`   📁 ${file.name}: ${sizeKB}KB`);
      totalSize += stats.size;
    }
  });
  
  console.log(`   📦 总大小: ${(totalSize / 1024).toFixed(1)}KB\n`);
  
} catch (error) {
  console.error(`   ❌ 文件统计失败: ${error.message}\n`);
}

console.log('🎉 导入测试完成！');
console.log('\n📖 使用说明:');
console.log('  CommonJS: const { RAGAnything } = require("./dist/index.js");');
console.log('  UMD:      <script src="./dist/index.umd.js"></script>');
console.log('  TypeScript: import { RAGAnything } from "./dist/index.d.ts";');
