/**
 * dts-bundle-generator 配置文件
 * 用于生成单个类型声明文件
 */

module.exports = {
  entries: [
    {
      filePath: './src/index.ts',
      outFile: './dist/index.d.ts',
      noCheck: false,
      output: {
        noBanner: false,
        respectPreserveConstEnum: true,
        exportReferencedTypes: true,
        sortNodes: true,
        inlineDeclareGlobals: false,
        inlineDeclareExternals: false
      }
    }
  ],
  compilationOptions: {
    preferredConfigPath: './tsconfig.json',
    followSymlinks: true,
    allowJs: false
  }
};
